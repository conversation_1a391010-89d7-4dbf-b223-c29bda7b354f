"""
PDF处理器模块 - 处理PDF附件
功能：
1. 自动下载PDF附件
2. 自动提取文本
3. 自动分析文本
4. 自动发送回复邮件
5. 使用docling库解析PDF
"""
import os
import sys
import tempfile
import requests
from typing import Optional, Dict, Any
from loguru import logger

# 添加项目根目录到sys.path
base_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(base_dir))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.core.ai_analyzer import AnalysisResult
from src.core.processor_manager import BaseProcessor
from src.utils.smtp_sender import send_email
from src.utils.email_utils import EmailUtils

try:
    from docling.document_converter import DocumentConverter
    DOCLING_AVAILABLE = True
except ImportError:
    logger.warning("docling库未安装，PDF文本提取功能将不可用")
    DOCLING_AVAILABLE = False

def send_email_reply(to: str, subject: str, body: str):
    """发送邮件回复"""
    logger.info(f"准备发送邮件回复:")
    logger.info(f"收件人: {to}")
    logger.info(f"主题: {subject}")
    logger.info(f"内容: {body}")

    # 使用SMTP发送邮件
    try:
        success = send_email(
            to=to,
            subject=subject,
            body=body
        )

        if success:
            logger.info(f"PDF处理结果邮件发送成功: {to}")
            return True
        else:
            logger.error(f"PDF处理结果邮件发送失败: {to}")
            return False

    except Exception as e:
        logger.error(f"发送PDF处理结果邮件时出错: {e}")
        return False

class PdfProcessor(BaseProcessor):
    def __init__(self, name: str = "pdf_processor", config: Optional[Dict[str, Any]] = None):
        super().__init__(name, config)
        # 从配置加载启用状态
        self.enabled = self.config.get('enabled', True)
        self.temp_dir = tempfile.gettempdir()
        self.download_path = os.path.join(self.temp_dir, "pdf_attachments")
        os.makedirs(self.download_path, exist_ok=True)

    def can_process(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> bool:
        """检查邮件是否包含PDF附件"""
        if not self.enabled:
            return False

        # 检查是否有PDF附件
        attachments = email_data.get('attachments', [])
        logger.debug(f"检查邮件附件: {len(attachments)}个附件")

        for att in attachments:
            content_type = att.get('content_type', '').lower()
            filename = att.get('filename', '').lower()

            logger.debug(f"附件: {filename}, 类型: {content_type}")

            # 检查MIME类型或文件扩展名
            if 'pdf' in content_type or filename.endswith('.pdf'):
                logger.info(f"发现PDF附件: {filename}")
                return True

        return False

    def download_pdf(self, url: str, filename: str) -> str:
        """下载PDF文件到临时目录"""
        filepath = os.path.join(self.download_path, filename)
        try:
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            with open(filepath, 'wb') as f:
                f.write(response.content)
            logger.info(f"下载PDF成功: {filename}")
            return filepath
        except Exception as e:
            logger.error(f"下载PDF失败: {e}")
            return ""

    def extract_text(self, filepath: str) -> str:
        """使用docling提取PDF文本"""
        if not DOCLING_AVAILABLE:
            logger.warning("docling库不可用，无法提取PDF文本")
            return "PDF文本提取功能不可用（需要安装docling库）"

        try:
            converter = DocumentConverter()
            result = converter.convert(filepath)
            # 根据docling文档，转换成功后直接获取文本
            text = result.document.export_to_text()
            logger.info(f"提取PDF文本成功: {os.path.basename(filepath)}")
            return text
        except Exception as e:
            logger.error(f"提取PDF文本失败: {e}")
            return f"PDF文本提取失败: {str(e)}"

    def analyze_text(self, text: str) -> Dict[str, Any]:
        """分析PDF文本内容"""
        import re
        from collections import Counter

        # 基本统计
        lines = text.split('\n')
        pages = text.split('\f')
        words = text.split()

        # 字符统计
        char_count = len(text)
        char_count_no_spaces = len(text.replace(' ', '').replace('\n', '').replace('\t', ''))

        # 段落统计
        paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]

        # 提取关键词（简单实现）
        # 移除标点符号和数字，提取常见词汇
        clean_text = re.sub(r'[^\w\s]', ' ', text.lower())
        clean_text = re.sub(r'\d+', ' ', clean_text)
        word_list = [word for word in clean_text.split() if len(word) > 3]
        word_freq = Counter(word_list)
        keywords = [word for word, count in word_freq.most_common(10) if count > 1]

        # 检测语言（简单判断）
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        english_words = len(re.findall(r'[a-zA-Z]+', text))
        language = "中文" if chinese_chars > english_words else "英文" if english_words > 0 else "未知"

        return {
            "page_count": len(pages),
            "line_count": len(lines),
            "paragraph_count": len(paragraphs),
            "word_count": len(words),
            "char_count": char_count,
            "char_count_no_spaces": char_count_no_spaces,
            "language": language,
            "keywords": keywords[:5] if keywords else ["无关键词"],
            "text_preview": text[:200] + "..." if len(text) > 200 else text,
            "has_tables": "表格" in text or "Table" in text,
            "has_images": "图" in text or "Figure" in text or "Fig" in text
        }

    def _generate_detailed_reply(self, filename: str, analysis_result: Dict[str, Any]) -> str:
        """生成详细的PDF处理结果邮件内容"""
        # 提取发送者姓名（如果可能）
        from src.utils.email_utils import EmailUtils

        reply_body = f"""您好！

您发送的PDF文件 '{filename}' 已成功处理完成。以下是详细的分析结果：

📄 **文档基本信息**
• 页数: {analysis_result['page_count']} 页
• 总行数: {analysis_result['line_count']} 行
• 段落数: {analysis_result['paragraph_count']} 段
• 语言: {analysis_result['language']}

📊 **文本统计**
• 总字数: {analysis_result['word_count']} 字
• 总字符数: {analysis_result['char_count']} 字符
• 有效字符数: {analysis_result['char_count_no_spaces']} 字符（不含空格）

🔍 **内容分析**
• 关键词: {', '.join(analysis_result['keywords'])}
• 包含表格: {'是' if analysis_result['has_tables'] else '否'}
• 包含图片: {'是' if analysis_result['has_images'] else '否'}

📝 **内容预览**
{analysis_result['text_preview']}

---
此邮件由邮件处理机器人自动发送，如有问题请联系管理员。
处理时间: {self._get_current_time()}
"""
        return reply_body

    def _get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def process(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> Dict[str, Any]:
        """处理PDF附件邮件"""
        results = []
        # 修复：使用正确的键名获取发送者信息
        sender = email_data.get('sender', '') or email_data.get('from', '')
        subject = email_data.get('subject', '无主题')

        # 调试日志
        logger.debug(f"PDF处理器 - 发送者: {sender}, 主题: {subject}")
        logger.debug(f"邮件数据键: {list(email_data.keys())}")

        if not sender:
            logger.error("无法获取邮件发送者信息，跳过PDF处理")
            return {"pdf_processing_results": [], "error": "无发送者信息"}
        
        for attachment in email_data.get('attachments', []):
            content_type = attachment.get('content_type', '').lower()
            filename = attachment.get('filename', 'unnamed.pdf').lower()
            
            # 检查是否是PDF附件
            if 'pdf' in content_type or filename.endswith('.pdf'):
                # 直接使用附件内容（不再需要下载URL）
                file_content = attachment.get('content', b'')
                
                if not file_content:
                    continue
                    
                # 保存到临时文件
                filepath = os.path.join(self.download_path, filename)
                with open(filepath, 'wb') as f:
                    f.write(file_content)
                logger.info(f"保存PDF附件: {filename}")
                    
                # 提取文本
                text = self.extract_text(filepath)
                if not text:
                    continue
                    
                # 分析文本
                analysis_result = self.analyze_text(text)
                
                # 发送回复邮件
                reply_subject = f"Re: {subject} - PDF处理完成"
                reply_body = self._generate_detailed_reply(filename, analysis_result)
                
                send_email_reply(
                    to=sender,
                    subject=reply_subject,
                    body=reply_body
                )
                logger.info(f"已发送PDF处理结果给 {sender}")
                
                results.append({
                    "filename": filename,
                    "analysis": analysis_result,
                    "reply_sent": True
                })
        
        return {"pdf_processing_results": results}